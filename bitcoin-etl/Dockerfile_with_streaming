FROM python:3.8
LABEL maintainer="BLCDevs <<EMAIL>>"
LABEL description="Bitcoin Node & On-Chain ETL Pipeline with Streaming"
ENV PROJECT_DIR=bitcoin-node-etl

RUN mkdir /$PROJECT_DIR
WORKDIR /$PROJECT_DIR
COPY . .
RUN pip install --upgrade pip && pip install -e /$PROJECT_DIR/[streaming]

# Add Tini
ENV TINI_VERSION v0.18.0
ADD https://github.com/krallin/tini/releases/download/${TINI_VERSION}/tini /tini
RUN chmod +x /tini

ENTRYPOINT ["/tini", "--", "python", "bitcoinetl"]
