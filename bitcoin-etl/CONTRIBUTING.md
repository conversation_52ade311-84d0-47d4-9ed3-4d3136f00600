# Contributing to Bitcoin Node & On-Chain ETL Pipeline

Thank you for your interest in contributing to the Bitcoin Node & On-Chain ETL Pipeline! This document provides guidelines and information for contributors.

## 🚀 Getting Started

### Prerequisites
- Python 3.8 or higher
- Bitcoin Core node with `txindex=1` enabled
- Git for version control
- Basic understanding of Bitcoin blockchain structure

### Development Setup

1. **Fork and Clone**
   ```bash
   git clone https://github.com/altapplabs/bitcoin-node-etl.git
   cd bitcoin-node-etl
   ```

2. **Create Virtual Environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install Development Dependencies**
   ```bash
   pip install -e .[dev,all]
   ```

4. **Run Tests**
   ```bash
   pytest tests/
   ```

## 📋 How to Contribute

### 🐛 Reporting Bugs

Before creating bug reports, please check existing issues to avoid duplicates.

**Bug Report Template:**
- **Description**: Clear description of the bug
- **Steps to Reproduce**: Detailed steps to reproduce the issue
- **Expected Behavior**: What you expected to happen
- **Actual Behavior**: What actually happened
- **Environment**: OS, Python version, Bitcoin Core version
- **Logs**: Relevant error messages or logs

### 💡 Suggesting Enhancements

Enhancement suggestions are welcome! Please provide:
- **Clear Description**: What enhancement you'd like to see
- **Use Case**: Why this enhancement would be useful
- **Implementation Ideas**: Any thoughts on how it could be implemented

### 🔧 Code Contributions

#### Pull Request Process

1. **Create Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make Changes**
   - Write clean, documented code
   - Follow existing code style
   - Add tests for new functionality
   - Update documentation as needed

3. **Test Your Changes**
   ```bash
   # Run tests
   pytest tests/
   
   # Run linting
   flake8 bitcoinetl/
   
   # Format code
   black bitcoinetl/
   ```

4. **Commit Changes**
   ```bash
   git add .
   git commit -m "feat: add new feature description"
   ```

5. **Push and Create PR**
   ```bash
   git push origin feature/your-feature-name
   ```

#### Code Style Guidelines

- **Python Style**: Follow PEP 8
- **Formatting**: Use Black for code formatting
- **Linting**: Use flake8 for linting
- **Documentation**: Use Google-style docstrings
- **Type Hints**: Use type hints where appropriate

#### Commit Message Format

Use conventional commits format:
- `feat:` for new features
- `fix:` for bug fixes
- `docs:` for documentation changes
- `test:` for test additions/changes
- `refactor:` for code refactoring
- `perf:` for performance improvements

## 🧪 Testing

### Running Tests
```bash
# Run all tests
pytest

# Run specific test file
pytest tests/test_specific.py

# Run with coverage
pytest --cov=bitcoinetl tests/
```

### Writing Tests
- Write unit tests for new functions
- Include integration tests for complex features
- Test edge cases and error conditions
- Maintain test coverage above 80%

## 📚 Documentation

### Code Documentation
- Use clear, descriptive function and variable names
- Add docstrings to all public functions and classes
- Include type hints for function parameters and return values
- Comment complex logic and algorithms

### User Documentation
- Update README.md for new features
- Add examples for new functionality
- Update command reference documentation
- Include configuration examples

## 🏗️ Architecture Guidelines

### Project Structure
```
bitcoinetl/
├── cli/           # Command-line interface
├── domain/        # Data models and entities
├── jobs/          # ETL job implementations
├── mappers/       # Data transformation logic
├── rpc/           # Bitcoin RPC client
├── service/       # Business logic services
└── streaming/     # Real-time streaming components
```

### Design Principles
- **Modularity**: Keep components loosely coupled
- **Testability**: Write testable code with dependency injection
- **Performance**: Optimize for large-scale data processing
- **Reliability**: Handle errors gracefully with proper logging
- **Extensibility**: Design for easy addition of new features

## 🤝 Community Guidelines

### Code of Conduct
- Be respectful and inclusive
- Welcome newcomers and help them learn
- Focus on constructive feedback
- Maintain professional communication

### Getting Help
- Check existing documentation first
- Search existing issues and discussions
- Ask questions in GitHub Discussions
- Join our community channels

## 📄 License

By contributing to this project, you agree that your contributions will be licensed under the MIT License.

## 🙏 Recognition

Contributors will be recognized in:
- CONTRIBUTORS.md file
- Release notes for significant contributions
- Project documentation

Thank you for contributing to the Bitcoin Node & On-Chain ETL Pipeline!
