# Changelog

All notable changes to the Bitcoin Node & On-Chain ETL Pipeline will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [2.0.0] - 2024-07-24

### 🚀 Major Release - Complete Platform Overhaul

#### Added
- **UTXO Snapshot Generation**: Real-time UTXO tracking for realized cap calculations
- **Price Data Integration**: CoinGecko API integration for market data
- **Parquet Output Support**: Columnar storage format for analytics
- **DuckDB Integration**: In-process analytical database support
- **ClickHouse Support**: High-performance OLAP database integration
- **Enhanced Streaming**: Real-time data pipeline with multiple output formats
- **Professional Documentation**: Comprehensive guides and API documentation
- **Docker Compose Setup**: Production-ready containerized deployment
- **Monitoring & Logging**: Built-in observability and alerting
- **Analytics Dashboard**: Streamlit-based visualization interface

#### Enhanced
- **Performance Optimization**: 3x faster data processing
- **Error Handling**: Robust fault tolerance and recovery mechanisms
- **Data Validation**: Comprehensive data quality checks
- **Memory Management**: Optimized for large-scale datasets
- **Configuration Management**: YAML-based configuration system
- **Testing Suite**: Comprehensive unit and integration tests

#### Changed
- **Project Name**: Rebranded to "Bitcoin Node & On-Chain ETL Pipeline"
- **Package Name**: Changed from `bitcoin-etl` to `bitcoin-node-etl`
- **Python Requirements**: Minimum Python 3.8+ (was 3.5+)
- **Dependencies**: Updated to latest stable versions
- **CLI Interface**: Enhanced command-line interface with better UX
- **Data Schema**: Extended schema with additional metadata fields

#### Security
- **Input Validation**: Enhanced security for RPC connections
- **Credential Management**: Secure handling of API keys and passwords
- **Network Security**: TLS/SSL support for all external connections

### 🔧 Technical Improvements
- Migrated to modern Python packaging standards
- Implemented comprehensive logging framework
- Added configuration validation
- Enhanced error reporting and debugging
- Optimized memory usage for large datasets
- Improved concurrent processing capabilities

### 📊 Analytics Features
- MVRV (Market Value to Realized Value) calculation support
- NUPL (Net Unrealized Profit/Loss) metrics
- SOPR (Spent Output Profit Ratio) analysis
- Address clustering capabilities
- Transaction flow analysis
- Custom metrics framework

### 🐛 Bug Fixes
- Fixed memory leaks in long-running processes
- Resolved timezone handling issues
- Corrected transaction fee calculations
- Fixed edge cases in UTXO tracking
- Improved error handling for network interruptions

---

## [1.5.2] - 2021-12-17 (Legacy)

### Added
- Taproot support for Bitcoin transactions

### Fixed
- Various bug fixes and improvements

---

## Previous Versions

For historical changes prior to v2.0.0, please refer to the original project documentation.

---

## Migration Guide

### From v1.x to v2.0.0

1. **Update Installation**:
   ```bash
   pip uninstall bitcoin-etl
   pip install bitcoin-node-etl
   ```

2. **Update Command Names**:
   - `bitcoinetl` → `bitcoin-node-etl`
   - Old commands still work for backward compatibility

3. **Configuration Changes**:
   - Review new configuration options in `config.yaml`
   - Update any custom scripts to use new parameter names

4. **Data Schema**:
   - New fields added to output schema
   - Existing fields remain compatible

For detailed migration instructions, see our [Migration Guide](docs/migration.md).
