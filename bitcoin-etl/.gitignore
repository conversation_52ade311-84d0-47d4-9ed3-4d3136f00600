# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

output/
data/
*.parquet
*.duckdb
*.db
*.sqlite
logs/
.streamlit/
config.yaml
config.json
# C extensions
*.so

# Distribution / packaging
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# dotenv
.env

# virtualenv
.venv
venv/
ENV/
