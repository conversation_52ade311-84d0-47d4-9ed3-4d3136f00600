import os

from setuptools import setup, find_packages


def read(fname):
    return open(os.path.join(os.path.dirname(__file__), fname)).read()


long_description = read('README.md') if os.path.isfile("README.md") else ""

setup(
    name='bitcoin-node-etl',
    version='2.0.0',
    author='AltAppLabs',
    author_email='<EMAIL>',
    description='Professional Bitcoin Node & On-Chain ETL Pipeline - Extract, Transform, Load blockchain data for analytics',
    long_description=long_description,
    long_description_content_type='text/markdown',
    url='https://github.com/altapplabs/bitcoin-node-etl',
    packages=find_packages(exclude=['tests']),
    classifiers=[
        'Development Status :: 5 - Production/Stable',
        'Intended Audience :: Developers',
        'License :: OSI Approved :: MIT License',
        'Programming Language :: Python :: 3',
        'Programming Language :: Python :: 3.5',
        'Programming Language :: Python :: 3.6',
        'Programming Language :: Python :: 3.7'
    ],
    keywords='bitcoin blockchain etl analytics data-engineering cryptocurrency utxo parquet duckdb',
    python_requires='>=3.8.0,<4',
    install_requires=[
        'requests>=2.25.0',
        'python-dateutil>=2.8.0',
        'click>=8.0.0',
        'pandas>=1.3.0',
        'pyarrow>=5.0.0',  # For Parquet support
        'duckdb>=0.8.0',   # For analytics database
    ],
    extras_require={
        'streaming': [
            'timeout-decorator>=0.4.1',
            'google-cloud-pubsub>=2.0.0',
            'kafka-python>=2.0.0',
        ],
        'analytics': [
            'clickhouse-driver>=0.2.0',
            'sqlalchemy>=1.4.0',
            'plotly>=5.0.0',
            'streamlit>=1.0.0',
        ],
        'dev': [
            'pytest>=6.0.0',
            'pytest-cov>=2.10.0',
            'black>=21.0.0',
            'flake8>=3.8.0',
        ],
        'all': [
            'timeout-decorator>=0.4.1',
            'google-cloud-pubsub>=2.0.0',
            'kafka-python>=2.0.0',
            'clickhouse-driver>=0.2.0',
            'sqlalchemy>=1.4.0',
            'plotly>=5.0.0',
            'streamlit>=1.0.0',
        ],
    },
    entry_points={
        'console_scripts': [
            'bitcoin-node-etl=bitcoinetl.cli:cli',
            'bitcoinetl=bitcoinetl.cli:cli',  # Backward compatibility
        ],
    },
    project_urls={
        'Bug Reports': 'https://github.com/altapplabs/bitcoin-node-etl/issues',
        'Documentation': 'https://github.com/altapplabs/bitcoin-node-etl/wiki',
        'Source': 'https://github.com/altapplabs/bitcoin-node-etl',
    },
)
